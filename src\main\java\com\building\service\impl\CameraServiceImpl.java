package com.building.service.impl;

import java.util.List;
import java.util.Map;

import com.building.dao.CameraDao;
import com.building.dao.PersonRecordDao;
import com.building.model.Camera;
import com.building.model.PersonRecord;
import com.building.service.CameraService;

/**
 * 摄像头服务实现类
 */
public class CameraServiceImpl implements CameraService {
    
    private CameraDao cameraDao;
    private PersonRecordDao personRecordDao;
    
    /**
     * 构造函数
     * 初始化DAO对象
     */
    public CameraServiceImpl() {
        cameraDao = new CameraDao();
        personRecordDao = new PersonRecordDao();
    }
    
    @Override
    public List<Camera> getAllCameras() {
        return cameraDao.getAllCameras();
    }
    
    @Override
    public Camera getCameraById(int id) {
        if (id <= 0) {
            return null;
        }
        return cameraDao.getCameraById(id);
    }
    
    @Override
    public List<Camera> getCamerasByRoomId(int roomId) {
        if (roomId <= 0) {
            return null;
        }
        return cameraDao.getCamerasByRoomId(roomId);
    }
    
    @Override
    public boolean addCamera(Camera camera) {
        if (camera == null || camera.getName() == null || camera.getName().trim().isEmpty() ||
            camera.getIpAddress() == null || camera.getIpAddress().trim().isEmpty()) {
            return false;
        }
        
        // 如果未设置RTSP URL，则自动生成
        if (camera.getRtspUrl() == null || camera.getRtspUrl().trim().isEmpty()) {
            String rtspUrl = generateRtspUrl(camera);
            camera.setRtspUrl(rtspUrl);
        }
        
        return cameraDao.addCamera(camera);
    }
    
    @Override
    public boolean updateCamera(Camera camera) {
        if (camera == null || camera.getId() <= 0 || camera.getName() == null || 
            camera.getName().trim().isEmpty() || camera.getIpAddress() == null || 
            camera.getIpAddress().trim().isEmpty()) {
            return false;
        }
        
        // 如果未设置RTSP URL，则自动生成
        if (camera.getRtspUrl() == null || camera.getRtspUrl().trim().isEmpty()) {
            String rtspUrl = generateRtspUrl(camera);
            camera.setRtspUrl(rtspUrl);
        }
        
        return cameraDao.updateCamera(camera);
    }
    
    @Override
    public boolean deleteCamera(int id) {
        if (id <= 0) {
            return false;
        }
        return cameraDao.deleteCamera(id);
    }
    
    @Override
    public boolean updateCameraStatus(int id, int status) {
        if (id <= 0 || (status != 0 && status != 1)) {
            return false;
        }
        return cameraDao.updateCameraStatus(id, status);
    }
    
    @Override
    public Map<String, Integer> getCameraStats() {
        return cameraDao.getCameraStats();
    }
    
    @Override
    public boolean addPersonRecord(PersonRecord record) {
        if (record == null || record.getCameraId() <= 0 || record.getRoomId() <= 0) {
            return false;
        }
        return personRecordDao.addRecord(record);
    }
    
    @Override
    public PersonRecord getLatestPersonRecord(int roomId) {
        if (roomId <= 0) {
            return null;
        }
        return personRecordDao.getLatestRecordByRoomId(roomId);
    }
    
    @Override
    public List<PersonRecord> getPersonRecordHistory(int roomId, int limit) {
        if (roomId <= 0 || limit <= 0) {
            return null;
        }
        return personRecordDao.getRecordHistoryByRoomId(roomId, limit);
    }
    
    @Override
    public List<PersonRecord> getPersonRecordsByTimeRange(int roomId, String startTime, String endTime) {
        if (roomId <= 0 || startTime == null || endTime == null) {
            return null;
        }
        return personRecordDao.getRecordsByTimeRange(roomId, startTime, endTime);
    }

    @Override
    public boolean disconnectAllCameras() {
        try {
            // 获取所有摄像头
            List<Camera> cameras = cameraDao.getAllCameras();
            if (cameras == null || cameras.isEmpty()) {
                System.out.println("没有摄像头需要断开连接");
                return true;
            }

            boolean allSuccess = true;
            int disconnectedCount = 0;

            // 逐个断开摄像头连接
            for (Camera camera : cameras) {
                if (camera.getStatus() == 1) { // 只断开在线的摄像头
                    boolean success = cameraDao.updateCameraStatus(camera.getId(), 0);
                    if (success) {
                        disconnectedCount++;
                        System.out.println("摄像头断开成功: " + camera.getName() + " (ID: " + camera.getId() + ")");
                    } else {
                        allSuccess = false;
                        System.err.println("摄像头断开失败: " + camera.getName() + " (ID: " + camera.getId() + ")");
                    }
                }
            }

            System.out.println("批量断开摄像头完成，成功断开 " + disconnectedCount + " 个摄像头");
            return allSuccess;

        } catch (Exception e) {
            System.err.println("批量断开摄像头异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 根据摄像头信息生成RTSP URL
     * @param camera 摄像头对象
     * @return RTSP URL
     */
    private String generateRtspUrl(Camera camera) {
        // 根据不同品牌生成不同的RTSP URL
        String brand = camera.getBrand() != null ? camera.getBrand().toLowerCase() : "";
        String username = camera.getUsername() != null ? camera.getUsername() : "admin";
        String password = camera.getPassword() != null ? camera.getPassword() : "admin";
        String ip = camera.getIpAddress();
        int port = camera.getPort() > 0 ? camera.getPort() : 554;
        
        if (brand.contains("hikvision") || brand.contains("海康")) {
            return "rtsp://" + username + ":" + password + "@" + ip + ":" + port + "/h264/ch1/main/av_stream";
        } else if (brand.contains("dahua") || brand.contains("大华")) {
            return "rtsp://" + username + ":" + password + "@" + ip + ":" + port + "/cam/realmonitor?channel=1&subtype=0";
        } else {
            // 默认RTSP URL格式
            return "rtsp://" + username + ":" + password + "@" + ip + ":" + port + "/live/ch1";
        }
    }
}
