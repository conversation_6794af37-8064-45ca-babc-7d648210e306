package com.building.controller;

import com.building.model.ProductionLine;
import com.building.service.ProductionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.ArrayList;

@Controller
@RequestMapping("/production")
public class ProductionController {
    private static final Logger logger = LoggerFactory.getLogger(ProductionController.class);

    @Autowired(required = false)
    private ProductionService productionService;

    // 显示产线管理页面
    @RequestMapping("/list")
    public String list(Model model) {
        logger.info("ProductionController.list() 被调用");
        try {
            List<ProductionLine> productionLines;

            // 如果服务注入失败，使用示例数据
            if (productionService == null) {
                logger.warn("ProductionService未注入，使用示例数据");
                productionLines = createSampleData();
            } else {
                try {
                    productionLines = productionService.getAllProductionLines();
                    logger.info("成功从数据库获取产线列表");
                } catch (Exception e) {
                    logger.error("从数据库获取产线列表失败，使用示例数据", e);
                    productionLines = createSampleData();
                }
            }

            model.addAttribute("productionLines", productionLines);
            logger.info("成功获取产线列表，返回视图: production/list");
            return "production/list";
        } catch (Exception e) {
            logger.error("访问产线管理页面出错", e);
            return "error/500";
        }
    }

    /**
     * 创建示例数据
     */
    private List<ProductionLine> createSampleData() {
        List<ProductionLine> sampleData = new ArrayList<>();

        ProductionLine line1 = new ProductionLine();
        line1.setId(1);
        line1.setName("智能制造产线A");
        line1.setStatus("运行中");
        line1.setLastUpdated("2024-01-15 14:30:25");
        sampleData.add(line1);

        ProductionLine line2 = new ProductionLine();
        line2.setId(2);
        line2.setName("自动化装配线B");
        line2.setStatus("维护中");
        line2.setLastUpdated("2024-01-15 13:45:10");
        sampleData.add(line2);

        ProductionLine line3 = new ProductionLine();
        line3.setId(3);
        line3.setName("精密加工产线C");
        line3.setStatus("运行中");
        line3.setLastUpdated("2024-01-15 15:20:18");
        sampleData.add(line3);

        return sampleData;
    }

    // 显示产线详情
    @RequestMapping("/{id}")
    public String detail(@PathVariable int id, Model model) {
        logger.info("访问产线详情页面 - id: {}", id);
        try {
            ProductionLine productionLine = null;

            if (productionService != null) {
                try {
                    productionLine = productionService.getProductionLine(id);
                } catch (Exception e) {
                    logger.error("从数据库获取产线详情失败", e);
                }
            }

            // 如果数据库查询失败，从示例数据中查找
            if (productionLine == null) {
                List<ProductionLine> sampleData = createSampleData();
                productionLine = sampleData.stream()
                    .filter(line -> line.getId() == id)
                    .findFirst()
                    .orElse(null);
            }

            if (productionLine != null) {
                model.addAttribute("productionLine", productionLine);
                logger.info("成功获取产线信息");
                return "production/detail";
            }

            logger.info("未找到对应的产线");
            return "error/404";
        } catch (Exception e) {
            logger.error("访问产线详情页面出错", e);
            return "error/500";
        }
    }

    // 添加产线
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public String add(@RequestParam String name, @RequestParam String status) {
        logger.info("添加新产线 - name: {}, status: {}", name, status);

        if (productionService == null) {
            logger.warn("ProductionService未注入，无法添加产线");
            return "error";
        }

        try {
            ProductionLine productionLine = new ProductionLine();
            productionLine.setName(name);
            productionLine.setStatus(status);
            productionService.addProductionLine(productionLine);
            logger.info("产线添加成功");
            return "success";
        } catch (Exception e) {
            logger.error("添加产线失败", e);
            return "error";
        }
    }

    // 更新产线
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    public String update(@RequestParam int id, @RequestParam String name, @RequestParam String status) {
        logger.info("更新产线 - id: {}, name: {}, status: {}", id, name, status);

        if (productionService == null) {
            logger.warn("ProductionService未注入，无法更新产线");
            return "error";
        }

        try {
            ProductionLine productionLine = new ProductionLine();
            productionLine.setId(id);
            productionLine.setName(name);
            productionLine.setStatus(status);
            productionService.updateProductionLine(productionLine);
            logger.info("产线更新成功");
            return "success";
        } catch (Exception e) {
            logger.error("更新产线失败", e);
            return "error";
        }
    }

    // 删除产线
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    @ResponseBody
    public String delete(@PathVariable int id) {
        logger.info("删除产线 - id: {}", id);

        if (productionService == null) {
            logger.warn("ProductionService未注入，无法删除产线");
            return "error";
        }

        try {
            productionService.deleteProductionLine(id);
            logger.info("产线删除成功");
            return "success";
        } catch (Exception e) {
            logger.error("删除产线失败", e);
            return "error";
        }
    }

    // 获取产线信息（用于编辑）
    @RequestMapping("/get/{id}")
    @ResponseBody
    public ProductionLine get(@PathVariable int id) {
        logger.info("获取产线信息 - id: {}", id);

        if (productionService != null) {
            try {
                ProductionLine productionLine = productionService.getProductionLine(id);
                logger.info("成功获取产线信息");
                return productionLine;
            } catch (Exception e) {
                logger.error("从数据库获取产线信息失败", e);
            }
        }

        // 如果数据库查询失败，从示例数据中查找
        List<ProductionLine> sampleData = createSampleData();
        ProductionLine productionLine = sampleData.stream()
            .filter(line -> line.getId() == id)
            .findFirst()
            .orElse(null);

        if (productionLine != null) {
            logger.info("从示例数据获取产线信息");
        } else {
            logger.warn("未找到产线信息 - id: {}", id);
        }

        return productionLine;
    }

    /**
     * 调试方法 - 测试产线管理路由是否正常
     */
    @RequestMapping("/debug")
    @ResponseBody
    public String debug() {
        logger.info("ProductionController.debug() 被调用");

        StringBuilder result = new StringBuilder();
        result.append("ProductionController 调试信息:\n");
        result.append("- Controller 已正常加载\n");
        result.append("- 当前时间: ").append(new java.util.Date()).append("\n");
        result.append("- ProductionService 注入状态: ").append(productionService != null ? "成功" : "失败").append("\n");

        if (productionService != null) {
            try {
                List<ProductionLine> lines = productionService.getAllProductionLines();
                result.append("- 数据库连接状态: 成功\n");
                result.append("- 产线数量: ").append(lines.size()).append("\n");
            } catch (Exception e) {
                result.append("- 数据库连接状态: 失败 - ").append(e.getMessage()).append("\n");
            }
        }

        return result.toString();
    }
}