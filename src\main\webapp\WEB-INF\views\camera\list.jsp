<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="摄像头管理" />
    <jsp:param name="content" value="/WEB-INF/views/camera/content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 页面整体样式 */
        body {
            background-color: #f8f9fa;
        }

        /* 页面标题区域样式 */
        .page-header-card {
            border: 1px solid rgba(0,0,0,0.05);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }
        .page-icon-wrapper {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        .page-icon-wrapper i {
            font-size: 1.8rem;
            color: white;
        }
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
        }
        .page-subtitle {
            font-size: 1rem;
            line-height: 1.5;
        }
        .search-input-group {
            min-width: 280px;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .search-input-group .input-group-text {
            border: 1px solid #e9ecef;
            border-right: none;
        }
        .search-input-group .form-control {
            border: 1px solid #e9ecef;
            border-left: none;
        }
        .btn-add-camera {
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
            transition: all 0.3s ease;
        }
        .btn-add-camera:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,123,255,0.4);
        }

        /* 警告提示样式 */
        .alert-icon-wrapper {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255,255,255,0.2);
        }

        /* 统计卡片样式 */
        .stats-section {
            margin-bottom: 2rem;
        }
        .stats-card {
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            position: relative;
            border: none;
            transition: all 0.4s ease;
            min-height: 160px;
        }
        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.9;
            z-index: 0;
        }
        .stats-card.bg-primary::before {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stats-card.bg-success::before {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .stats-card.bg-danger::before {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        .stats-card .card-body {
            position: relative;
            z-index: 1;
        }
        .stats-content {
            flex: 1;
        }
        .stats-icon-container {
            width: 50px;
            height: 50px;
            background-color: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        .stats-card:hover .stats-icon-container {
            transform: scale(1.1);
            background-color: rgba(255,255,255,0.3);
        }
        .stats-icon {
            font-size: 1.5rem;
            color: white;
        }
        .stats-number {
            font-size: 2.2rem;
            font-weight: 800;
            color: white;
            line-height: 1;
        }
        .stats-label {
            font-size: 0.95rem;
            color: rgba(255,255,255,0.9);
            font-weight: 500;
        }
        .stats-trend {
            text-align: center;
            opacity: 0.8;
        }
        .stats-trend i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
            display: block;
        }
        .trend-text {
            font-size: 0.8rem;
            font-weight: 600;
        }

        /* 筛选工具栏样式 */
        .filter-section {
            margin-bottom: 2rem;
        }
        .filter-toolbar {
            border: 1px solid rgba(0,0,0,0.05);
        }
        .filter-section-title {
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }
        .filter-group {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }
        .filter-label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }
        .filter-btn-group {
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .filter-btn {
            border: none;
            background-color: #fff;
            color: #6c757d;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border-right: 1px solid #e9ecef;
        }
        .filter-btn:last-child {
            border-right: none;
        }
        .filter-btn:hover {
            background-color: #f8f9fa;
            color: #495057;
        }
        .filter-btn.active {
            background-color: #007bff;
            color: white;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 摄像头卡片样式 */
        .cameras-section {
            margin-bottom: 3rem;
        }
        .camera-card {
            border-radius: 16px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.05);
            margin-bottom: 25px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            overflow: hidden;
            position: relative;
        }
        .camera-card:hover {
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            transform: translateY(-5px);
        }
        .camera-card .card-header {
            border-bottom: none;
            padding: 1.25rem 1.5rem;
            background: linear-gradient(to right, #f8f9fa, #ffffff);
        }
        .camera-card .card-body {
            padding: 1.5rem;
        }
        .camera-card .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0,0,0,0.05);
            padding: 1.25rem 1.5rem;
        }
        .camera-status {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            position: relative;
        }
        .camera-status::after {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            border-radius: 50%;
            animation: pulse 2s infinite;
            opacity: 0;
        }
        .camera-online {
            background-color: #28a745;
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
        }
        .camera-online::after {
            background-color: rgba(40, 167, 69, 0.3);
        }
        .camera-offline {
            background-color: #dc3545;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
        }
        .camera-offline::after {
            background-color: rgba(220, 53, 69, 0.3);
        }
        @keyframes pulse {
            0% {
                transform: scale(0.8);
                opacity: 0.8;
            }
            70% {
                transform: scale(1.5);
                opacity: 0;
            }
            100% {
                transform: scale(1.5);
                opacity: 0;
            }
        }
        .camera-thumbnail {
            height: 180px;
            background-color: #f8f9fa;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 0 10px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .camera-card:hover .camera-thumbnail {
            box-shadow: inset 0 0 15px rgba(0,0,0,0.1);
        }
        .camera-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.5s ease;
        }
        .camera-card:hover .camera-thumbnail img {
            transform: scale(1.05);
        }
        .camera-thumbnail .camera-icon {
            font-size: 3.5rem;
            color: #adb5bd;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        .camera-card:hover .camera-thumbnail .camera-icon {
            opacity: 1;
            transform: scale(1.1);
        }
        .camera-info-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .camera-card:hover .camera-info-item {
            background-color: rgba(0,0,0,0.02);
        }
        .camera-info-item i {
            width: 24px;
            color: #6c757d;
            margin-right: 10px;
            text-align: center;
        }
        .camera-info-item span {
            flex: 1;
        }
        .btn-camera-action {
            border-radius: 50px;
            padding: 0.5rem 1.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .btn-camera-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        }
        .btn-camera-action i {
            margin-right: 5px;
        }
        /* 统计卡片样式 */
        .stats-card {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0,0,0,0.05);
            position: relative;
            border: none;
            transition: all 0.4s ease;
            min-height: 180px;
            max-height: 200px;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        .stats-card .card-body {
            padding: 1.5rem;
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100%;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.8;
            z-index: 0;
        }
        .stats-card.bg-primary::before {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        }
        .stats-card.bg-success::before {
            background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        }
        .stats-card.bg-danger::before {
            background: linear-gradient(135deg, #e74a3b 0%, #be2617 100%);
        }
        .stats-icon-container {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
        }
        .stats-card:hover .stats-icon-container {
            transform: scale(1.1) rotate(10deg);
            background-color: rgba(255,255,255,0.3);
        }
        .stats-icon {
            font-size: 2rem;
            color: white;
        }
        .stats-card h3 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.3rem;
        }
        .stats-card p {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 0.5rem;
        }
        .stats-trend {
            position: absolute;
            bottom: 1rem;
            right: 1rem;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
        }
        .stats-trend i {
            margin-right: 5px;
        }

        /* 空状态样式 */
        .empty-state-card {
            border: 2px dashed #e9ecef;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            min-height: 300px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .empty-state-icon i {
            font-size: 4rem;
            opacity: 0.5;
        }
        .empty-state-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #495057;
        }
        .empty-state-description {
            font-size: 1rem;
            max-width: 400px;
            line-height: 1.6;
        }

        /* 服务器管理面板样式 */
        .server-management-section {
            margin-top: 3rem;
            margin-bottom: 2rem;
        }
        .server-management-card {
            border: 1px solid rgba(0,0,0,0.05);
        }
        .server-management-card .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }
        .server-title-section h5 {
            font-size: 1.25rem;
            color: #2c3e50;
        }
        .server-title-section p {
            font-size: 0.9rem;
        }
        .server-actions .btn {
            font-size: 0.85rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
        }
        .status-indicator-wrapper .badge {
            font-size: 0.9rem;
            font-weight: 600;
        }
        .server-details {
            line-height: 1.6;
        }
        .server-address code {
            background-color: #f8f9fa;
            color: #e83e8c;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        .server-info {
            font-size: 0.85rem;
        }

        /* 原有筛选工具栏样式保持兼容 */
        .filter-toolbar.d-flex {
            background-color: #f8f9fa;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .filter-label {
            font-weight: 500;
            margin-right: 10px;
            color: #495057;
        }
        .filter-btn {
            border-radius: 20px;
            margin-right: 5px;
            font-size: 0.875rem;
            padding: 0.375rem 1rem;
            background-color: #fff;
            border: 1px solid #dee2e6;
            color: #495057;
            transition: all 0.2s ease;
        }
        .filter-btn:hover {
            background-color: #e9ecef;
        }
        .filter-btn.active {
            background-color: #007bff;
            border-color: #007bff;
            color: #fff;
        }
        /* 模态框样式 */
        .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .modal-header {
            background: linear-gradient(to right, #f8f9fa, #ffffff);
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
        }
        .modal-body {
            padding: 1.5rem;
        }
        .modal-footer {
            border-top: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
        }
        .form-floating {
            margin-bottom: 1.5rem;
        }
        .form-floating > .form-control {
            padding: 1.5rem 0.75rem 0.75rem;
            height: calc(3.5rem + 2px);
        }
        .form-floating > label {
            padding: 1rem 0.75rem;
        }
        .form-select {
            padding: 0.75rem;
            height: calc(3.5rem + 2px);
        }
        /* Toast 提示样式 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1060;
        }
        .toast {
            width: 350px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 10px;
            overflow: hidden;
        }
        .toast-header {
            border-bottom: none;
            padding: 0.75rem 1rem;
        }
        .toast-body {
            padding: 1rem;
        }
    " />
    <jsp:param name="scripts" value="
        <script>
            // 创建Toast提示
            function showToast(message, type = 'success') {
                const toastContainer = document.querySelector('.toast-container');
                if (!toastContainer) return;

                const toastId = 'toast-' + Date.now();
                const iconClass = type === 'success' ? 'bi-check-circle-fill text-success' : 'bi-exclamation-triangle-fill text-danger';
                const title = type === 'success' ? '成功' : '错误';

                // 使用DOM API创建元素，避免JSP解析问题
                const toastDiv = document.createElement('div');
                toastDiv.id = toastId;
                toastDiv.className = 'toast';
                toastDiv.setAttribute('role', 'alert');
                toastDiv.setAttribute('aria-live', 'assertive');
                toastDiv.setAttribute('aria-atomic', 'true');

                const toastHeader = document.createElement('div');
                toastHeader.className = 'toast-header';

                const icon = document.createElement('i');
                icon.className = 'bi ' + iconClass + ' me-2';

                const strong = document.createElement('strong');
                strong.className = 'me-auto';
                strong.textContent = title;

                const small = document.createElement('small');
                small.textContent = '刚刚';

                const closeButton = document.createElement('button');
                closeButton.className = 'btn-close';
                closeButton.setAttribute('data-bs-dismiss', 'toast');
                closeButton.setAttribute('aria-label', 'Close');

                const toastBody = document.createElement('div');
                toastBody.className = 'toast-body';
                toastBody.textContent = message;

                toastHeader.appendChild(icon);
                toastHeader.appendChild(strong);
                toastHeader.appendChild(small);
                toastHeader.appendChild(closeButton);

                toastDiv.appendChild(toastHeader);
                toastDiv.appendChild(toastBody);

                toastContainer.appendChild(toastDiv);
                const toastElement = document.getElementById(toastId);
                const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
                toast.show();

                // 自动移除
                toastElement.addEventListener('hidden.bs.toast', function() {
                    toastElement.remove();
                });
            }

            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 显示页面初始化提示
                showToast('正在初始化摄像头管理页面，断开所有连接并清除缓存...', 'info');

                // 初始化表单验证
                const forms = document.querySelectorAll('.needs-validation');
                Array.from(forms).forEach(form => {
                    form.addEventListener('submit', event => {
                        if (!form.checkValidity()) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });

                // 初始化搜索功能
                const searchInput = document.getElementById('cameraSearchInput');
                if (searchInput) {
                    searchInput.addEventListener('keyup', function() {
                        filterCameras();
                    });
                }

                // 筛选按钮点击事件
                const filterButtons = document.querySelectorAll('.filter-btn[data-filter]');
                filterButtons.forEach(btn => {
                    btn.addEventListener('click', function() {
                        // 移除同组按钮的active类
                        document.querySelectorAll('.filter-btn[data-filter]').forEach(b => b.classList.remove('active'));
                        // 添加当前按钮的active类
                        this.classList.add('active');
                        // 筛选摄像头
                        filterCameras();
                    });
                });

                // 位置筛选按钮点击事件
                const locationButtons = document.querySelectorAll('.filter-btn[data-location]');
                locationButtons.forEach(btn => {
                    btn.addEventListener('click', function() {
                        // 移除同组按钮的active类
                        document.querySelectorAll('.filter-btn[data-location]').forEach(b => b.classList.remove('active'));
                        // 添加当前按钮的active类
                        this.classList.add('active');
                        // 筛选摄像头
                        filterCameras();
                    });
                });

                // 筛选摄像头函数
                function filterCameras() {
                    const searchText = searchInput ? searchInput.value.toLowerCase() : '';
                    const statusFilter = document.querySelector('.filter-btn[data-filter].active').getAttribute('data-filter');
                    const locationFilter = document.querySelector('.filter-btn[data-location].active').getAttribute('data-location');

                    const cameraItems = document.querySelectorAll('.camera-item');

                    cameraItems.forEach(item => {
                        const cameraName = item.querySelector('.camera-name').textContent.toLowerCase();
                        const cameraLocation = item.querySelector('.camera-location').textContent.toLowerCase();
                        const cameraStatus = item.getAttribute('data-status');
                        const cameraLocationAttr = item.getAttribute('data-location');

                        // 检查是否匹配搜索文本
                        const matchesSearch = cameraName.includes(searchText) || cameraLocation.includes(searchText);

                        // 检查是否匹配状态筛选
                        const matchesStatus = statusFilter === 'all' || cameraStatus === statusFilter;

                        // 检查是否匹配位置筛选
                        const matchesLocation = locationFilter === 'all' || cameraLocationAttr === locationFilter;

                        // 显示或隐藏摄像头
                        if (matchesSearch && matchesStatus && matchesLocation) {
                            item.style.display = '';
                        } else {
                            item.style.display = 'none';
                        }
                    });

                    // 检查是否有可见的摄像头
                    // 使用filter而不是属性选择器，避免JSP解析问题
                    const allCameras = document.querySelectorAll('.camera-item');
                    const visibleCameras = Array.from(allCameras).filter(item => item.style.display === 'none');
                    const noResultsElement = document.getElementById('noResultsMessage');

                    if (visibleCameras.length === cameraItems.length) {
                        // 如果没有可见的摄像头，显示无结果消息
                        if (!noResultsElement) {
                            const cameraList = document.getElementById('cameraList');
                            // 创建元素而不是使用HTML字符串，避免JSP解析问题
                            const noResultsDiv = document.createElement('div');
                            noResultsDiv.id = 'noResultsMessage';
                            noResultsDiv.className = 'col-12';

                            const alertDiv = document.createElement('div');
                            alertDiv.className = 'alert alert-info text-center py-4 rounded-4 shadow-sm';

                            const icon = document.createElement('i');
                            icon.className = 'bi bi-search fs-1 mb-3 d-block';

                            const heading = document.createElement('h4');
                            heading.textContent = '未找到匹配的摄像头';

                            const paragraph = document.createElement('p');
                            paragraph.className = 'mb-0';
                            paragraph.textContent = '请尝试调整搜索条件';

                            alertDiv.appendChild(icon);
                            alertDiv.appendChild(heading);
                            alertDiv.appendChild(paragraph);
                            noResultsDiv.appendChild(alertDiv);

                            cameraList.appendChild(noResultsDiv);
                        }
                    } else if (noResultsElement) {
                        // 如果有可见的摄像头，移除无结果消息
                        noResultsElement.remove();
                    }
                }

                // 添加摄像头表单提交
                const addCameraForm = document.getElementById('addCameraForm');
                if (addCameraForm) {
                    addCameraForm.addEventListener('submit', function(e) {
                        e.preventDefault();

                        if (!this.checkValidity()) {
                            e.stopPropagation();
                            this.classList.add('was-validated');
                            return;
                        }

                        // 获取表单数据
                        const formData = new FormData(this);
                        const formDataObj = {};
                        formData.forEach((value, key) => {
                            formDataObj[key] = value;
                        });

                        // 发送AJAX请求
                        fetch('${pageContext.request.contextPath}/camera/add', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: new URLSearchParams(formDataObj).toString()
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // 关闭模态框
                                const modal = bootstrap.Modal.getInstance(document.getElementById('addCameraModal'));
                                modal.hide();

                                // 显示成功提示
                                showToast(data.message, 'success');

                                // 延迟刷新页面
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            } else {
                                showToast(data.message || '添加失败', 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('添加失败，请稍后重试', 'error');
                        });
                    });
                }

                // 连接摄像头
                window.connectCamera = function(cameraId) {
                    fetch('${pageContext.request.contextPath}/camera/control', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'cameraId=' + cameraId + '&action=connect'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showToast(data.message, 'success');
                            // 延迟刷新页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            showToast(data.message || '连接失败', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('连接失败，请稍后重试', 'error');
                    });
                };

                // 断开摄像头
                window.disconnectCamera = function(cameraId) {
                    fetch('${pageContext.request.contextPath}/camera/control', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'cameraId=' + cameraId + '&action=disconnect'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showToast(data.message, 'success');
                            // 延迟刷新页面
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            showToast(data.message || '断开失败', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('断开失败，请稍后重试', 'error');
                    });
                };

                // 查看视频流
                window.viewStream = function(cameraId) {
                    window.location.href = '${pageContext.request.contextPath}/camera/stream?id=' + cameraId;
                };

                // 查看摄像头详情
                window.viewCameraDetail = function(cameraId) {
                    window.location.href = '${pageContext.request.contextPath}/camera/detail?id=' + cameraId;
                };

                // 删除摄像头
                window.deleteCamera = function(cameraId, cameraName) {
                    // 设置模态框中的摄像头名称
                    document.getElementById('deleteCameraName').textContent = cameraName;

                    // 显示确认删除模态框
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteCameraModal'));
                    deleteModal.show();

                    // 绑定确认删除按钮事件
                    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
                    confirmDeleteBtn.onclick = function() {
                        // 发送删除请求
                        fetch('${pageContext.request.contextPath}/camera/delete', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'id=' + cameraId
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                showToast('摄像头删除成功', 'success');
                                // 关闭模态框
                                deleteModal.hide();
                                // 延迟刷新页面
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            } else {
                                showToast(data.message || '删除失败', 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('删除失败，请稍后重试', 'error');
                        });
                    };
                };

                // 流服务器管理功能
                window.checkStreamServerStatus = function() {
                    fetch('${pageContext.request.contextPath}/stream-server/manage?action=status')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                updateServerStatusIndicator(data.isRunning);
                                showToast('服务器状态检查完成: ' + data.message, 'success');
                            } else {
                                showToast('检查服务器状态失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('检查服务器状态失败，请稍后重试', 'error');
                        });
                };

                window.startStreamServer = function() {
                    if (confirm('确定要启动视频流服务器吗？')) {
                        showToast('正在启动视频流服务器...', 'success');

                        fetch('${pageContext.request.contextPath}/stream-server/manage', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'action=start'
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                updateServerStatusIndicator(true);
                                showToast(data.message, 'success');
                            } else {
                                showToast('启动失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('启动失败，请稍后重试', 'error');
                        });
                    }
                };

                window.stopStreamServer = function() {
                    if (confirm('确定要停止视频流服务器吗？这将中断所有正在进行的视频流。')) {
                        showToast('正在停止视频流服务器...', 'success');

                        fetch('${pageContext.request.contextPath}/stream-server/manage', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'action=stop'
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                updateServerStatusIndicator(false);
                                showToast(data.message, 'success');
                            } else {
                                showToast('停止失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('停止失败，请稍后重试', 'error');
                        });
                    }
                };

                window.restartStreamServer = function() {
                    if (confirm('确定要重启视频流服务器吗？这将暂时中断所有正在进行的视频流。')) {
                        showToast('正在重启视频流服务器...', 'success');

                        fetch('${pageContext.request.contextPath}/stream-server/manage', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'action=restart'
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                updateServerStatusIndicator(true);
                                showToast(data.message, 'success');
                            } else {
                                showToast('重启失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('重启失败，请稍后重试', 'error');
                        });
                    }
                };

                window.showStreamServerDetails = function() {
                    fetch('${pageContext.request.contextPath}/stream-server/manage?action=status')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const details = data.healthDetails || '无详细信息';
                                alert('视频流服务器详细信息:' + String.fromCharCode(10) + String.fromCharCode(10) + details);
                            } else {
                                showToast('获取详细信息失败: ' + data.message, 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showToast('获取详细信息失败，请稍后重试', 'error');
                        });
                };

                function updateServerStatusIndicator(isRunning) {
                    const indicator = document.getElementById('serverStatusIndicator');
                    if (indicator) {
                        indicator.className = 'badge ' + (isRunning ? 'bg-success' : 'bg-danger') + ' rounded-pill px-3 py-2';
                        const iconClass = isRunning ? 'check-circle-fill' : 'x-circle-fill';
                        const statusText = isRunning ? '运行中' : '未运行';
                        indicator.innerHTML = '<i class=\"bi bi-' + iconClass + ' me-1\"></i>' + statusText;
                    }
                }

                // 添加动画效果
                const cameraCards = document.querySelectorAll('.camera-card');
                cameraCards.forEach((card, index) => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100 + index * 50);
                });

                // 统计卡片动画
                const statsCards = document.querySelectorAll('.stats-card');
                statsCards.forEach((card, index) => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100 + index * 100);
                });

                // 延迟显示初始化完成提示
                setTimeout(() => {
                    showToast('摄像头管理页面初始化完成，所有设备已断开连接，缓存已清除', 'success');
                }, 2000);
            });
        </script>
    " />
</jsp:include>
