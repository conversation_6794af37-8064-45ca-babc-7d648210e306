const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const WebSocket = require('ws');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 存储活动的流进程
const activeStreams = new Map();

// 配置信息
const config = {
    ffmpegPath: 'ffmpeg', // FFmpeg可执行文件路径
    outputFormats: ['flv', 'hls', 'm3u8'],
    defaultFormat: 'hls'
};

// 检查FFmpeg是否可用
function checkFFmpeg() {
    return new Promise((resolve, reject) => {
        const ffmpeg = spawn('ffmpeg', ['-version']);
        ffmpeg.on('close', (code) => {
            if (code === 0) {
                resolve(true);
            } else {
                reject(new Error('FFmpeg not found'));
            }
        });
        ffmpeg.on('error', (err) => {
            reject(err);
        });

        // 设置超时
        setTimeout(() => {
            ffmpeg.kill();
            reject(new Error('FFmpeg check timeout'));
        }, 5000);
    });
}

// 测试RTSP连接
function testRTSPConnection(rtspUrl) {
    return new Promise((resolve, reject) => {
        console.log(`测试RTSP连接: ${rtspUrl}`);

        const testArgs = [
            '-i', rtspUrl,
            '-t', '5', // 只测试5秒
            '-f', 'null',
            '-'
        ];

        const testProcess = spawn('ffmpeg', testArgs);
        let hasError = false;
        let errorMessage = '';

        testProcess.stderr.on('data', (data) => {
            const msg = data.toString();
            if (msg.includes('Connection refused') ||
                msg.includes('Connection timed out') ||
                msg.includes('Invalid data found') ||
                msg.includes('Server returned 401 Unauthorized') ||
                msg.includes('Server returned 404 Not Found') ||
                msg.includes('No route to host')) {
                hasError = true;
                errorMessage = msg;
            }
        });

        testProcess.on('close', (code) => {
            if (hasError) {
                reject(new Error(`RTSP连接测试失败: ${errorMessage}`));
            } else {
                resolve(true);
            }
        });

        testProcess.on('error', (err) => {
            reject(err);
        });

        // 设置超时
        setTimeout(() => {
            testProcess.kill();
            if (!hasError) {
                resolve(true); // 如果没有错误且超时，认为连接成功
            }
        }, 10000);
    });
}

// 启动RTSP转码流
function startRTSPStream(rtspUrl, streamId, format = 'hls') {
    return new Promise(async (resolve, reject) => {
        // 如果流已经存在，先停止它
        if (activeStreams.has(streamId)) {
            stopStream(streamId);
        }

        // 先测试RTSP连接
        try {
            console.log(`开始测试RTSP连接: ${streamId}`);
            await testRTSPConnection(rtspUrl);
            console.log(`RTSP连接测试成功: ${streamId}`);
        } catch (error) {
            console.error(`RTSP连接测试失败: ${streamId}, 错误: ${error.message}`);
            reject(error);
            return;
        }

        let ffmpegArgs;
        let outputUrl;

        if (format === 'flv') {
            // 使用管道输出到HTTP流
            outputUrl = `/live/${streamId}.flv`;
            ffmpegArgs = [
                '-i', rtspUrl,
                '-c:v', 'libx264',
                '-profile:v', 'baseline',
                '-level', '3.0',
                '-preset', 'ultrafast',
                '-tune', 'zerolatency',
                '-c:a', 'aac',
                '-ar', '44100',
                '-ac', '2',
                '-f', 'flv',
                '-flvflags', 'no_duration_filesize',
                '-fflags', '+genpts',
                '-avoid_negative_ts', 'make_zero',
                'pipe:1'  // 输出到stdout
            ];
        } else if (format === 'hls') {
            // 确保HLS输出目录存在
            const hlsDir = path.join(__dirname, 'public', 'hls', streamId);
            if (!fs.existsSync(hlsDir)) {
                fs.mkdirSync(hlsDir, { recursive: true });
            }

            outputUrl = `/hls/${streamId}/playlist.m3u8`;
            ffmpegArgs = [
                '-rtsp_transport', 'tcp',  // 使用TCP传输，更稳定
                '-i', rtspUrl,
                '-c:v', 'libx264',
                '-profile:v', 'baseline',
                '-level', '3.0',
                '-preset', 'veryfast',
                '-tune', 'zerolatency',
                '-g', '50',
                '-keyint_min', '25',
                '-sc_threshold', '0',
                '-c:a', 'aac',
                '-ar', '44100',
                '-ac', '2',
                '-b:a', '128k',
                '-f', 'hls',
                '-hls_time', '2',
                '-hls_list_size', '8',
                '-hls_flags', 'delete_segments+append_list+round_durations',
                '-hls_allow_cache', '0',
                '-hls_segment_type', 'mpegts',
                '-hls_segment_filename', path.join(hlsDir, 'segment_%03d.ts'),
                '-force_key_frames', 'expr:gte(t,n_forced*2)',
                '-timeout', '30000000',  // 30秒超时
                '-reconnect', '1',       // 启用重连
                '-reconnect_at_eof', '1', // EOF时重连
                '-reconnect_streamed', '1', // 流式重连
                path.join(hlsDir, 'playlist.m3u8')
            ];
        }

        console.log('启动FFmpeg进程:', ffmpegArgs.join(' '));

        const ffmpeg = spawn('ffmpeg', ffmpegArgs);
        
        ffmpeg.stdout.on('data', (data) => {
            console.log(`FFmpeg stdout: ${data}`);
        });

        ffmpeg.stderr.on('data', (data) => {
            const errorMsg = data.toString();
            console.log(`FFmpeg stderr: ${errorMsg}`);

            // 检查是否是致命错误
            if (errorMsg.includes('Connection refused') ||
                errorMsg.includes('Connection timed out') ||
                errorMsg.includes('Invalid data found') ||
                errorMsg.includes('Server returned 401 Unauthorized') ||
                errorMsg.includes('Server returned 404 Not Found') ||
                errorMsg.includes('No route to host')) {
                console.error(`FFmpeg致命错误检测到: ${errorMsg}`);
                const stream = activeStreams.get(streamId);
                if (stream) {
                    stream.autoRestart = false; // 停止自动重启
                    stream.errorCount = (stream.errorCount || 0) + 1;
                    if (stream.errorCount >= 3) {
                        console.error(`流 ${streamId} 错误次数过多，停止重试`);
                        activeStreams.delete(streamId);
                        return;
                    }
                }
            }
        });

        ffmpeg.on('close', (code) => {
            console.log(`FFmpeg进程退出，代码: ${code}`);
            const stream = activeStreams.get(streamId);
            if (stream && stream.autoRestart !== false) {
                // 增加重试计数和延迟
                stream.retryCount = (stream.retryCount || 0) + 1;
                const maxRetries = 5;
                const retryDelay = Math.min(2000 * stream.retryCount, 30000); // 最大30秒延迟

                if (stream.retryCount <= maxRetries) {
                    console.log(`自动重启流: ${streamId} (第${stream.retryCount}次重试，${retryDelay}ms后重试)`);
                    setTimeout(() => {
                        if (activeStreams.has(streamId)) {
                            console.log(`重新启动FFmpeg进程: ${streamId}`);
                            startRTSPStream(rtspUrl, streamId, format).catch(console.error);
                        }
                    }, retryDelay);
                } else {
                    console.error(`流 ${streamId} 重试次数已达上限，停止重试`);
                    activeStreams.delete(streamId);
                }
            } else {
                activeStreams.delete(streamId);
            }
        });

        ffmpeg.on('error', (err) => {
            console.error('FFmpeg错误:', err);
            const stream = activeStreams.get(streamId);
            if (stream && stream.autoRestart !== false) {
                console.log(`FFmpeg错误，准备重启: ${streamId}`);
                setTimeout(() => {
                    if (activeStreams.has(streamId)) {
                        console.log(`错误恢复，重新启动FFmpeg: ${streamId}`);
                        startRTSPStream(rtspUrl, streamId, format).catch(console.error);
                    }
                }, 5000);
            } else {
                activeStreams.delete(streamId);
                reject(err);
            }
        });

        // 存储流信息
        activeStreams.set(streamId, {
            process: ffmpeg,
            rtspUrl: rtspUrl,
            format: format,
            outputUrl: outputUrl,
            startTime: new Date(),
            autoRestart: true
        });

        // 如果是FLV格式，设置管道处理
        if (format === 'flv') {
            ffmpeg.stdout.on('data', (data) => {
                const stream = activeStreams.get(streamId);
                if (stream && stream.clients) {
                    stream.clients.forEach(client => {
                        if (!client.destroyed) {
                            client.write(data);
                        }
                    });
                }
            });

            const stream = activeStreams.get(streamId);
            if (stream) {
                stream.clients = [];
            }
        }

        // 等待更长时间确保流开始，特别是对于响应较慢的摄像头
        setTimeout(() => {
            // 检查进程是否还在运行
            if (ffmpeg && !ffmpeg.killed) {
                console.log(`流 ${streamId} 启动成功，进程正在运行`);
                resolve({
                    streamId: streamId,
                    outputUrl: outputUrl,
                    format: format
                });
            } else {
                console.error(`流 ${streamId} 启动失败，进程已退出`);
                reject(new Error('FFmpeg进程启动后立即退出'));
            }
        }, 8000); // 增加到8秒等待时间
    });
}

// 停止流
function stopStream(streamId) {
    const stream = activeStreams.get(streamId);
    if (stream) {
        stream.autoRestart = false;

        if (stream.clients) {
            stream.clients.forEach(client => {
                if (!client.destroyed) {
                    client.end();
                }
            });
        }

        stream.process.kill('SIGTERM');
        activeStreams.delete(streamId);

        // 清理HLS文件
        if (stream.format === 'hls') {
            const hlsDir = path.join(__dirname, 'public', 'hls', streamId);
            if (fs.existsSync(hlsDir)) {
                fs.rmSync(hlsDir, { recursive: true, force: true });
            }
        }

        return true;
    }
    return false;
}

// 实时FLV流路由
app.get('/live/:streamId.flv', (req, res) => {
    const streamId = req.params.streamId;
    const stream = activeStreams.get(streamId);

    if (!stream || stream.format !== 'flv') {
        return res.status(404).json({ error: '流不存在或格式不匹配' });
    }

    res.writeHead(200, {
        'Content-Type': 'video/x-flv',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Range',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Connection': 'keep-alive'
    });

    if (!stream.clients) {
        stream.clients = [];
    }
    stream.clients.push(res);

    req.on('close', () => {
        if (stream.clients) {
            const index = stream.clients.indexOf(res);
            if (index > -1) {
                stream.clients.splice(index, 1);
            }
        }
    });

    req.on('error', () => {
        if (stream.clients) {
            const index = stream.clients.indexOf(res);
            if (index > -1) {
                stream.clients.splice(index, 1);
            }
        }
    });
});

// API路由

// 测试RTSP连接
app.post('/api/rtsp/test', async (req, res) => {
    try {
        const { rtspUrl } = req.body;

        if (!rtspUrl) {
            return res.status(400).json({
                error: '缺少必要参数: rtspUrl'
            });
        }

        await testRTSPConnection(rtspUrl);
        res.json({
            success: true,
            message: 'RTSP连接测试成功',
            data: {
                rtspUrl: rtspUrl,
                status: 'connected'
            }
        });
    } catch (error) {
        console.error('RTSP连接测试失败:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            data: {
                rtspUrl: req.body.rtspUrl,
                status: 'failed'
            }
        });
    }
});

// 启动流
app.post('/api/stream/start', async (req, res) => {
    try {
        const { rtspUrl, streamId, format } = req.body;

        if (!rtspUrl || !streamId) {
            return res.status(400).json({
                error: '缺少必要参数: rtspUrl 和 streamId'
            });
        }

        const result = await startRTSPStream(rtspUrl, streamId, format);
        res.json({
            success: true,
            message: '流启动成功',
            data: result
        });
    } catch (error) {
        console.error('启动流失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 停止流
app.post('/api/stream/stop', (req, res) => {
    try {
        const { streamId } = req.body;
        
        if (!streamId) {
            return res.status(400).json({ 
                error: '缺少参数: streamId' 
            });
        }

        const stopped = stopStream(streamId);
        if (stopped) {
            res.json({
                success: true,
                message: '流停止成功'
            });
        } else {
            res.status(404).json({
                success: false,
                error: '流不存在'
            });
        }
    } catch (error) {
        console.error('停止流失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取流状态
app.get('/api/stream/status', (req, res) => {
    const streams = Array.from(activeStreams.entries()).map(([id, stream]) => ({
        streamId: id,
        rtspUrl: stream.rtspUrl,
        format: stream.format,
        outputUrl: stream.outputUrl,
        startTime: stream.startTime,
        uptime: Date.now() - stream.startTime.getTime()
    }));

    res.json({
        success: true,
        data: {
            activeStreams: streams.length,
            streams: streams
        }
    });
});

// 停止所有活跃的流
app.post('/api/stream/stop-all', (req, res) => {
    try {
        const streamIds = Array.from(activeStreams.keys());
        let stoppedCount = 0;

        for (const streamId of streamIds) {
            if (stopStream(streamId)) {
                stoppedCount++;
            }
        }

        res.json({
            success: true,
            message: `成功停止 ${stoppedCount} 个活跃流`,
            data: {
                stoppedCount: stoppedCount,
                totalStreams: streamIds.length
            }
        });
    } catch (error) {
        console.error('停止所有流失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 清除所有视频缓存
app.post('/api/cache/clear', (req, res) => {
    try {
        const hlsBaseDir = path.join(__dirname, 'public', 'hls');
        let clearedCount = 0;

        // 清除HLS缓存目录
        if (fs.existsSync(hlsBaseDir)) {
            const streamDirs = fs.readdirSync(hlsBaseDir);
            for (const streamDir of streamDirs) {
                const streamPath = path.join(hlsBaseDir, streamDir);
                if (fs.statSync(streamPath).isDirectory()) {
                    fs.rmSync(streamPath, { recursive: true, force: true });
                    clearedCount++;
                    console.log(`清除缓存目录: ${streamPath}`);
                }
            }
        }

        // 清除其他可能的缓存文件
        const publicDir = path.join(__dirname, 'public');
        const tempFiles = ['*.tmp', '*.temp'];

        res.json({
            success: true,
            message: `成功清除 ${clearedCount} 个缓存目录`,
            data: {
                clearedDirectories: clearedCount,
                cacheBasePath: hlsBaseDir
            }
        });
    } catch (error) {
        console.error('清除缓存失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 健康检查
app.get('/api/health', async (req, res) => {
    try {
        await checkFFmpeg();
        res.json({
            success: true,
            message: 'Service is healthy',
            ffmpeg: 'available'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Service unhealthy',
            error: error.message
        });
    }
});

// 启动服务器
app.listen(PORT, '0.0.0.0', async () => {
    console.log(`EduFusionCenter RTSP转码服务器运行在端口 ${PORT}`);
    console.log(`服务器地址: http://localhost:${PORT}`);
    console.log(`服务器地址: http://127.0.0.1:${PORT}`);

    try {
        await checkFFmpeg();
        console.log('✓ FFmpeg 可用');
    } catch (error) {
        console.error('✗ FFmpeg 不可用:', error.message);
        console.log('请确保已安装FFmpeg并添加到系统PATH中');
    }
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('正在关闭服务器...');
    
    for (const [streamId] of activeStreams) {
        stopStream(streamId);
    }
    
    process.exit(0);
});
