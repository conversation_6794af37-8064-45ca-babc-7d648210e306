package com.building.service;

import com.building.model.Camera;

/**
 * 视频流服务接口
 * 定义视频流相关的业务逻辑
 */
public interface VideoStreamService {
    
    /**
     * 启动摄像头视频流
     * @param cameraId 摄像头ID
     * @return 是否启动成功
     */
    boolean startCameraStream(int cameraId);
    
    /**
     * 停止摄像头视频流
     * @param cameraId 摄像头ID
     * @return 是否停止成功
     */
    boolean stopCameraStream(int cameraId);
    
    /**
     * 重启摄像头视频流
     * @param cameraId 摄像头ID
     * @return 是否重启成功
     */
    boolean restartCameraStream(int cameraId);
    
    /**
     * 获取摄像头流状态
     * @param cameraId 摄像头ID
     * @return 流状态信息
     */
    String getCameraStreamStatus(int cameraId);
    
    /**
     * 检查流服务器健康状态
     * @return 是否健康
     */
    boolean checkStreamServerHealth();
    
    /**
     * 获取摄像头流URL
     * @param camera 摄像头对象
     * @return 流URL
     */
    String getCameraStreamUrl(Camera camera);

    /**
     * 启动流服务器
     * @return 是否启动成功
     */
    boolean startStreamServer();

    /**
     * 停止流服务器
     * @return 是否停止成功
     */
    boolean stopStreamServer();

    /**
     * 检查流服务器是否运行
     * @return 服务器是否运行中
     */
    boolean isStreamServerRunning();

    /**
     * 获取流服务器详细健康状态
     * @return 健康状态信息
     */
    String getStreamServerHealthDetails();

    /**
     * 检查用户是否手动停止了流服务器
     * @return 是否手动停止
     */
    boolean isUserManuallyStopped();

    /**
     * 清除所有视频缓存
     * @return 是否清除成功
     */
    boolean clearAllVideoCache();

    /**
     * 停止所有活跃的视频流
     * @return 是否停止成功
     */
    boolean stopAllActiveStreams();
}
