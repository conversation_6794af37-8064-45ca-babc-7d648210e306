package com.building.service.impl;

import com.building.dao.ProductionDao;
import com.building.model.ProductionLine;
import com.building.service.ProductionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProductionServiceImpl implements ProductionService {
    private static final Logger logger = LoggerFactory.getLogger(ProductionServiceImpl.class);

    @Autowired(required = false)
    private ProductionDao productionDao;

    @Override
    public List<ProductionLine> getAllProductionLines() {
        if (productionDao == null) {
            logger.error("ProductionDao未注入，无法获取产线列表");
            throw new RuntimeException("ProductionDao未注入");
        }
        logger.info("获取所有产线列表");
        return productionDao.getAllProductionLines();
    }

    @Override
    public void addProductionLine(ProductionLine line) {
        if (productionDao == null) {
            logger.error("ProductionDao未注入，无法添加产线");
            throw new RuntimeException("ProductionDao未注入");
        }
        logger.info("添加产线: {}", line.getName());
        productionDao.addProductionLine(line);
    }

    @Override
    public ProductionLine getProductionLine(int id) {
        if (productionDao == null) {
            logger.error("ProductionDao未注入，无法获取产线信息");
            throw new RuntimeException("ProductionDao未注入");
        }
        logger.info("获取产线信息: id={}", id);
        return productionDao.getProductionLine(id);
    }

    @Override
    public void updateProductionLine(ProductionLine line) {
        if (productionDao == null) {
            logger.error("ProductionDao未注入，无法更新产线");
            throw new RuntimeException("ProductionDao未注入");
        }
        logger.info("更新产线: id={}, name={}", line.getId(), line.getName());
        productionDao.updateProductionLine(line);
    }

    @Override
    public void deleteProductionLine(int id) {
        if (productionDao == null) {
            logger.error("ProductionDao未注入，无法删除产线");
            throw new RuntimeException("ProductionDao未注入");
        }
        logger.info("删除产线: id={}", id);
        productionDao.deleteProductionLine(id);
    }
} 