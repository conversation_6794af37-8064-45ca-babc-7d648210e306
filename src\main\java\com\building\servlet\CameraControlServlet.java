package com.building.servlet;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.building.model.Camera;
import com.building.service.CameraService;
import com.building.service.impl.CameraServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 摄像头控制Servlet
 * 用于处理摄像头控制的请求
 */
@WebServlet("/camera/control")
public class CameraControlServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private CameraService cameraService;
    private ObjectMapper objectMapper;
    
    @Override
    public void init() throws ServletException {
        cameraService = new CameraServiceImpl();
        objectMapper = new ObjectMapper();
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 设置响应类型
        response.setContentType("application/json;charset=UTF-8");
        
        // 创建结果Map
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取请求参数
            String cameraIdStr = request.getParameter("cameraId");
            String action = request.getParameter("action");
            
            // 参数验证
            if (cameraIdStr == null || cameraIdStr.trim().isEmpty() || 
                action == null || action.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "摄像头ID和操作不能为空");
                objectMapper.writeValue(response.getWriter(), result);
                return;
            }
            
            int cameraId = Integer.parseInt(cameraIdStr);
            
            // 获取摄像头信息
            Camera camera = cameraService.getCameraById(cameraId);
            if (camera == null) {
                result.put("success", false);
                result.put("message", "摄像头不存在");
                objectMapper.writeValue(response.getWriter(), result);
                return;
            }
            
            // 根据不同的操作执行不同的控制逻辑
            boolean success = false;
            String message = "";
            
            switch (action) {
                case "connect":
                    // 连接摄像头
                    success = cameraService.updateCameraStatus(cameraId, 1);
                    message = success ? "摄像头连接成功" : "摄像头连接失败";
                    break;
                    
                case "disconnect":
                    // 断开摄像头
                    success = cameraService.updateCameraStatus(cameraId, 0);
                    message = success ? "摄像头断开成功" : "摄像头断开失败";
                    break;

                case "disconnect_all":
                    // 断开所有摄像头
                    success = cameraService.disconnectAllCameras();
                    message = success ? "所有摄像头断开成功" : "批量断开摄像头失败";
                    break;
                    
                case "pan_left":
                case "pan_right":
                case "tilt_up":
                case "tilt_down":
                case "zoom_in":
                case "zoom_out":
                    // 这些控制功能需要通过摄像头API实现
                    // 目前仅返回模拟成功
                    success = true;
                    message = "摄像头控制命令已发送";
                    break;
                    
                default:
                    success = false;
                    message = "不支持的操作: " + action;
                    break;
            }
            
            result.put("success", success);
            result.put("message", message);
            
        } catch (NumberFormatException e) {
            result.put("success", false);
            result.put("message", "无效的摄像头ID");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "处理请求时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 返回JSON结果
        objectMapper.writeValue(response.getWriter(), result);
    }
}
